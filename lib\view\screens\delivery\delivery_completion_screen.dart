import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:kisankonnect_rider/routes/app_pages.dart';
import 'package:kisankonnect_rider/services/all_services.dart';
import 'package:kisankonnect_rider/view/widgets/cancel_delivery_dialog.dart';
import 'package:kisankonnect_rider/view/widgets/switch_order_dialog.dart';
import 'package:kisankonnect_rider/view/widgets/cash_received_dialog.dart';
import 'package:kisankonnect_rider/view/widgets/qr_code_dialog.dart';
import 'package:kisankonnect_rider/view/widgets/partial_payment_dialog.dart';

import 'package:kisankonnect_rider/view/widgets/quality_issue_dialog.dart';

class DeliveryCompletionScreen extends StatefulWidget {
  final String customerName;
  final String orderId;

  const DeliveryCompletionScreen({
    super.key,
    this.customerName = 'Prathamesh Chavan (Gold)',
    this.orderId = 'E76592',
  });

  @override
  State<DeliveryCompletionScreen> createState() => _DeliveryCompletionScreenState();
}

class _DeliveryCompletionScreenState extends State<DeliveryCompletionScreen> {
  bool _showOrderDetails = false;
  String _selectedPaymentMode = 'Cash';
  List<String> _otpDigits = ['', '', '', ''];
  List<FocusNode> _focusNodes = List.generate(4, (index) => FocusNode());
  List<TextEditingController> _controllers = List.generate(4, (index) => TextEditingController());

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Get.back(),
        ),
        title: Text(
          'Deliver to ${widget.customerName}',
          style: TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          Container(
            margin: EdgeInsets.only(right: 16),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.green,
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.phone, color: Colors.white, size: 20),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // MyGate OTP Section
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.home_outlined, size: 20, color: Colors.grey.shade600),
                      SizedBox(width: 8),
                      Text(
                        'MyGate OTP',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildOTPBox('1'),
                      _buildOTPBox('4'),
                      _buildOTPBox('6'),
                      _buildOTPBox('9'),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(height: 20),

            // Order Details Section
            GestureDetector(
              onTap: () {
                setState(() {
                  _showOrderDetails = !_showOrderDetails;
                });
              },
              child: Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(Icons.lock_outline, size: 20, color: Colors.grey.shade600),
                    SizedBox(width: 8),
                    Text(
                      'Order details',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                    Spacer(),
                    Text(
                      '15 items . 01 Saddle bag . 01 Silver bag',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    SizedBox(width: 8),
                    Icon(
                      _showOrderDetails ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                      color: Colors.grey.shade600,
                    ),
                  ],
                ),
              ),
            ),

            if (_showOrderDetails) ...[
              SizedBox(height: 12),
              // Product images
              Container(
                height: 60,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: [
                    _buildProductImage('assets/images/product1.jpg'),
                    _buildProductImage('assets/images/product2.jpg'),
                    _buildProductImage('assets/images/product3.jpg'),
                    _buildProductImage('assets/images/product4.jpg'),
                    _buildProductImage('assets/images/product5.jpg'),
                  ],
                ),
              ),
              SizedBox(height: 8),
              // Items short notification
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Text(
                      'Items short - 2',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Spacer(),
                    Icon(Icons.keyboard_arrow_up, color: Colors.red, size: 16),
                  ],
                ),
              ),
            ],
            SizedBox(height: 20),

            // Payment Status
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Color(0xFFFFF3CD),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(Icons.account_balance_wallet, size: 20, color: Colors.orange),
                  SizedBox(width: 8),
                  Text(
                    'Payment status',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'COD: Take ₹1000 from the customer',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16),

            // Payment Mode Selection
            Text(
              'Payment mode',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildPaymentModeButton('Cash', Icons.money, _selectedPaymentMode == 'Cash'),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: _buildPaymentModeButton('QR code', Icons.qr_code, _selectedPaymentMode == 'QR code'),
                ),
              ],
            ),
            SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: _buildPaymentModeButton(
                  'Partial cash & QR Code', Icons.payment, _selectedPaymentMode == 'Partial cash & QR Code'),
            ),
            SizedBox(height: 20),

            // Take Photo Section
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.camera_alt_outlined, size: 20, color: Colors.grey.shade600),
                      SizedBox(width: 8),
                      Text(
                        'Take photo of items',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {},
                          icon: Icon(Icons.error_outline, color: AppColors.green, size: 16),
                          label: Text(
                            'Item missing',
                            style: TextStyle(color: AppColors.green, fontSize: 12),
                          ),
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: AppColors.green),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            // Show quality issue dialog
                            showQualityIssueDialog();
                          },
                          icon: Icon(Icons.report_outlined, color: AppColors.green, size: 16),
                          label: Text(
                            'Quality issue',
                            style: TextStyle(color: AppColors.green, fontSize: 12),
                          ),
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: AppColors.green),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // Navigate to delivery camera screen
                        Get.toNamed(AppRoutes.deliveryCamera);
                      },
                      icon: Icon(Icons.camera_alt, color: Colors.white, size: 18),
                      label: Text(
                        'Take photo of delivery',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.green,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(24),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 20),

            // Customer Address
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.location_on_outlined, size: 20, color: Colors.grey.shade600),
                SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Customer address',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          SizedBox(width: 4),
                          Icon(Icons.verified, size: 14, color: AppColors.green),
                        ],
                      ),
                      SizedBox(height: 4),
                      Text(
                        'NLT-B, room no 36, Sector 20, Opposite to Dreamland society,Nerul (w), Navi Mumbai',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                          height: 1.3,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),

            // Delivery Done Button
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: () {
                  // Check payment mode and show appropriate dialog
                  if (_selectedPaymentMode == 'Cash') {
                    // Show cash received dialog
                    showCashReceivedDialog(amount: 1000.00);
                  } else if (_selectedPaymentMode == 'QR code') {
                    // Show QR code dialog
                    showQRCodeDialog(amount: 1000.00);
                  } else if (_selectedPaymentMode == 'Partial cash & QR Code') {
                    // Show partial payment dialog
                    showPartialPaymentDialog(totalAmount: 1000.00);
                  } else {
                    // Handle any other payment modes
                    Get.snackbar(
                      'Delivery Complete',
                      'Order has been delivered successfully!',
                      backgroundColor: AppColors.green,
                      colorText: Colors.white,
                    );
                    // Show OTP and chill pad dialog after delivery completion
                    Future.delayed(Duration(milliseconds: 500), () {
                      _showOTPAndChillPadDialog();
                    });
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey.shade300,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '>>',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Delivery Done',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // Additional Options
            ListTile(
              leading: Icon(Icons.swap_horiz, color: Colors.grey.shade600),
              title: Text(
                'Switch order',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
              onTap: () {
                showSwitchOrderDialog();
              },
            ),
            ListTile(
              leading: Icon(Icons.cancel_outlined, color: Colors.red),
              title: Text(
                'Cancel delivery',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.red,
                ),
              ),
              onTap: () {
                showCancelDeliveryDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductImage(String imagePath) {
    return Container(
      width: 50,
      height: 50,
      margin: EdgeInsets.only(right: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        Icons.image,
        color: Colors.grey.shade400,
        size: 24,
      ),
    );
  }

  Widget _buildPaymentModeButton(String title, IconData icon, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPaymentMode = title;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.green.withValues(alpha: 0.1) : Colors.white,
          border: Border.all(
            color: isSelected ? AppColors.green : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? AppColors.green : Colors.grey.shade600,
              size: 16,
            ),
            SizedBox(width: 6),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isSelected ? AppColors.green : Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showOTPAndChillPadDialog() {
    List<String> otpDigits = ['', '', '', ''];
    String chillPadCount = '';

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: double.maxFinite,
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with close button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Enter OTP & Chill pad count',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Get.back(),
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.close,
                        color: Colors.grey.shade600,
                        size: 18,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 24),

              // Enter customer OTP section
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Enter customer OTP',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
              ),
              SizedBox(height: 12),

              // OTP input boxes
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(4, (index) {
                  return Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Center(
                      child: TextField(
                        textAlign: TextAlign.center,
                        keyboardType: TextInputType.number,
                        maxLength: 1,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          counterText: '',
                        ),
                        onChanged: (value) {
                          if (value.length == 1 && index < 3) {
                            FocusScope.of(Get.context!).nextFocus();
                          }
                          otpDigits[index] = value;
                        },
                      ),
                    ),
                  );
                }),
              ),
              SizedBox(height: 24),

              // Enter chill pad count section
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Enter chill pad count',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
              ),
              SizedBox(height: 12),

              // Chill pad count input
              Container(
                width: double.infinity,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: TextField(
                  keyboardType: TextInputType.number,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 15),
                    hintText: 'Enter count',
                    hintStyle: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 16,
                    ),
                  ),
                  onChanged: (value) {
                    chillPadCount = value;
                  },
                ),
              ),
              SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  // Back button
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Get.back(),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: AppColors.green),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        padding: EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: Text(
                        'Back',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.green,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 16),

                  // Submit button
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // Handle OTP and chill pad count submission
                        String otp = otpDigits.join();
                        Get.back();
                        // Navigate to feedback screen
                        Get.toNamed(AppRoutes.feedback);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade400,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        padding: EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: Text(
                        'Submit',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  Widget _buildOTPBox(String digit) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Text(
          digit,
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
