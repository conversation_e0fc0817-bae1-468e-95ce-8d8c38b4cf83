import 'package:get/get.dart';
import 'package:kisankonnect_rider/view/screens/splash/splash_screen.dart';
import 'package:kisankonnect_rider/view/screens/login/login.dart';
import 'package:kisankonnect_rider/view/screens/login/enter_number_screen.dart';
import 'package:kisankonnect_rider/view/screens/create_profile/stepper/create_profile_screen.dart';
import 'package:kisankonnect_rider/view/screens/delivery_partner/delivery_partner_welcome_screen.dart';
import 'package:kisankonnect_rider/view/screens/home/<USER>';
import 'package:kisankonnect_rider/view/screens/create_profile/stepper/profile_summary_screen.dart';
import 'package:kisankonnect_rider/view/screens/wallet/wallet_screen.dart';
import 'package:kisankonnect_rider/view/screens/all_earnings/all_earnings_screen.dart';
import 'package:kisankonnect_rider/view/screens/home/<USER>/refer_earn_content.dart';
import 'package:kisankonnect_rider/view/screens/scanner/qr_scanner_screen.dart';
import 'package:kisankonnect_rider/view/screens/orders/pickup_order_screen.dart';
import 'package:kisankonnect_rider/view/screens/orders/enhanced_pickup_order_screen.dart';
import 'package:kisankonnect_rider/view/screens/orders/order_summary_screen.dart';
import 'package:kisankonnect_rider/view/screens/orders/order_picked_up_screen.dart';
import 'package:kisankonnect_rider/view/screens/delivery/on_the_way_screen.dart';
import 'package:kisankonnect_rider/view/screens/delivery/delivery_completion_screen.dart';
import 'package:kisankonnect_rider/view/screens/delivery/cod_payment_screen.dart';
import 'package:kisankonnect_rider/view/screens/delivery/customer_unavailable_screen.dart';
import 'package:kisankonnect_rider/view/screens/delivery/online_payment_verification_screen.dart';
import 'package:kisankonnect_rider/view/screens/delivery/otp_verification_screen.dart';
import 'package:kisankonnect_rider/view/screens/orders/order_pickup_page.dart';
import 'package:kisankonnect_rider/view/screens/orders/order_scanning_screen.dart';
import 'package:kisankonnect_rider/view/screens/orders/first_order_delivery_screen.dart';
import 'package:kisankonnect_rider/view/screens/camera/delivery_camera_screen.dart';
import 'package:kisankonnect_rider/view/screens/feedback/feedback_screen.dart';

import 'package:kisankonnect_rider/screens/flavor_info_screen.dart';
import 'package:kisankonnect_rider/view/screens/test/api_key_test_screen.dart';
import 'package:kisankonnect_rider/bindings/app_bindings.dart';

/// App Routes - Registration Focus Only
class AppRoutes {
  // Core App Flow
  static const splash = '/splash';
  static const login = '/login';
  static const enterNumber = '/enter-number';

  // Registration Flow
  static const createProfile = '/create-profile';
  static const createProfileEnhanced = '/create-profile-enhanced';
  static const deliveryPartnerWelcome = '/delivery-partner-welcome';

  // Post Registration
  static const profileSummary = '/profile-summary';
  static const dashboard = '/dashboard';
  static const wallet = '/wallet';
  static const allEarnings = '/all-earnings';
  static const referEarn = '/refer-earn';

  // Order Management
  static const qrScanner = '/qr-scanner';
  static const pickupOrder = '/pickup-order';
  static const enhancedPickupOrder = '/enhanced-pickup-order';
  static const orderPickup = '/order-pickup';
  static const orderScanning = '/order-scanning';
  static const orderSummary = '/order-summary';
  static const orderPickedUp = '/order-picked-up';
  static const firstOrderDelivery = '/first-order-delivery';
  static const reachedSocietyGate = '/reached-society-gate';
  static const onTheWay = '/on-the-way';
  static const deliveryCompletion = '/delivery-completion';

  // Delivery Screens
  static const codPayment = '/cod-payment';
  static const customerUnavailable = '/customer-unavailable';
  static const onlinePaymentVerification = '/online-payment-verification';
  static const deliveryOtpVerification = '/delivery-otp-verification';
  static const deliveryCamera = '/delivery-camera';
  static const feedback = '/feedback';

  // Development
  static const flavorInfo = '/flavor-info';
  static const apiKeyTest = '/api-key-test';
}

class AppPages {
  static final pages = [
    // Core App Flow
    GetPage(name: AppRoutes.splash, page: () => const SplashScreen()),
    GetPage(name: AppRoutes.enterNumber, page: () => const EnterNumberScreen()),
    GetPage(name: AppRoutes.login, page: () => const LoginScreen()),

    // Registration Flow
    GetPage(
      name: AppRoutes.createProfile,
      page: () => const CreateProfileScreen(),
      binding: ProfileBindings(),
    ),

    GetPage(
      name: AppRoutes.deliveryPartnerWelcome,
      page: () => const DeliveryPartnerWelcomeScreen(),
    ),

    // Post Registration
    GetPage(
      name: AppRoutes.profileSummary,
      page: () => const ProfileSummaryScreen(),
    ),
    GetPage(
      name: AppRoutes.dashboard,
      page: () => const DashboardScreen(),
      binding: AppBindings(),
    ),
    GetPage(name: AppRoutes.wallet, page: () => const WalletScreen()),
    GetPage(name: AppRoutes.allEarnings, page: () => const AllEarningsScreen()),
    GetPage(name: AppRoutes.referEarn, page: () => const ReferEarnScreen()),

    // Order Management
    GetPage(
      name: AppRoutes.qrScanner,
      page: () {
        final args = Get.arguments as Map<String, dynamic>? ?? {};
        return QRScannerScreen(
          scanType: args['scanType'] ?? 'QR Code',
          orderId: args['orderId'] ?? '',
        );
      },
    ),
    GetPage(
      name: AppRoutes.pickupOrder,
      page: () {
        final args = Get.arguments as Map<String, dynamic>? ?? {};
        return PickupOrderScreen(
          totalOrders: args['totalOrders'] ?? 0,
        );
      },
    ),
    GetPage(
      name: AppRoutes.enhancedPickupOrder,
      page: () {
        final args = Get.arguments as Map<String, dynamic>? ?? {};
        return EnhancedPickupOrderScreen(
          totalOrders: args['totalOrders'] ?? 0,
        );
      },
    ),
    GetPage(
      name: AppRoutes.orderPickup,
      page: () => const OrderPickupPage(),
    ),
    GetPage(
      name: AppRoutes.orderScanning,
      page: () => const OrderScanningScreen(),
    ),
    GetPage(
      name: AppRoutes.firstOrderDelivery,
      page: () => const FirstOrderDeliveryScreen(),
    ),
    GetPage(
      name: AppRoutes.orderSummary,
      page: () => const OrderSummaryScreen(),
    ),
    GetPage(name: AppRoutes.orderPickedUp, page: () => const OrderPickedUpScreen()),
    GetPage(
      name: AppRoutes.onTheWay,
      page: () {
        final args = Get.arguments as Map<String, dynamic>? ?? {};
        return OnTheWayScreen(
          orderId: args['orderId'] ?? '',
          timeRemaining: args['timeRemaining'],
        );
      },
    ),
    GetPage(
      name: AppRoutes.deliveryCompletion,
      page: () => const DeliveryCompletionScreen(),
    ),

    // Delivery Screens
    GetPage(
      name: AppRoutes.codPayment,
      page: () => const CODPaymentScreen(),
    ),
    GetPage(
      name: AppRoutes.customerUnavailable,
      page: () {
        final args = Get.arguments as Map<String, dynamic>? ?? {};
        return CustomerUnavailableScreen(
          orderId: args['orderId'] ?? '',
          customerName: args['customerName'] ?? '',
          customerPhone: args['customerPhone'] ?? '',
          customerAddress: args['customerAddress'] ?? '',
        );
      },
    ),
    GetPage(
      name: AppRoutes.onlinePaymentVerification,
      page: () {
        final args = Get.arguments as Map<String, dynamic>? ?? {};
        return OnlinePaymentVerificationScreen(
          orderId: args['orderId'] ?? '',
          customerName: args['customerName'] ?? '',
          totalAmount: args['totalAmount'] ?? 0.0,
          paymentMethod: args['paymentMethod'] ?? 'upi',
        );
      },
    ),
    GetPage(
      name: AppRoutes.deliveryOtpVerification,
      page: () {
        final args = Get.arguments as Map<String, dynamic>? ?? {};
        return OTPVerificationScreen(
          orderId: args['orderId'] ?? '',
          customerName: args['customerName'] ?? '',
          customerPhone: args['customerPhone'] ?? '',
        );
      },
    ),
    GetPage(
      name: AppRoutes.deliveryCamera,
      page: () => const DeliveryCameraScreen(),
    ),
    GetPage(
      name: AppRoutes.feedback,
      page: () => const FeedbackScreen(),
    ),

    // Development
    GetPage(name: AppRoutes.flavorInfo, page: () => const FlavorInfoScreen()),
    GetPage(name: AppRoutes.apiKeyTest, page: () => const ApiKeyTestScreen()),
  ];
}
