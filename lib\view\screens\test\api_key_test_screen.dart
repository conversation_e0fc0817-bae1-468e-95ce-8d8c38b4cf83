import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:kisankonnect_rider/services/all_services.dart';

class ApiKeyTestScreen extends StatefulWidget {
  const ApiKeyTestScreen({super.key});

  @override
  State<ApiKeyTestScreen> createState() => _ApiKeyTestScreenState();
}

class _ApiKeyTestScreenState extends State<ApiKeyTestScreen> {
  String _testResult = 'Testing API key...';
  bool _isLoading = true;
  Color _resultColor = Colors.orange;

  @override
  void initState() {
    super.initState();
    _testApiKey();
  }

  Future<void> _testApiKey() async {
    final apiKey = MapsService.instance.apiKey;
    
    if (apiKey.isEmpty || apiKey == 'your_google_maps_api_key') {
      setState(() {
        _testResult = '❌ API Key is not configured';
        _resultColor = Colors.red;
        _isLoading = false;
      });
      return;
    }

    try {
      // Test with a simple geocoding request
      final url = 'https://maps.googleapis.com/maps/api/geocode/json?address=Mumbai&key=$apiKey';
      final response = await http.get(Uri.parse(url));
      final data = json.decode(response.body);

      if (data['status'] == 'OK') {
        setState(() {
          _testResult = '✅ API Key is valid and working!';
          _resultColor = Colors.green;
          _isLoading = false;
        });
      } else {
        setState(() {
          _testResult = '❌ API Key Error: ${data['error_message'] ?? data['status']}';
          _resultColor = Colors.red;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _testResult = '❌ Network Error: $e';
        _resultColor = Colors.red;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Google Maps API Key Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'API Key Information',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text('API Key: ${MapsService.instance.apiKey.substring(0, 8)}...'),
                    const SizedBox(height: 8),
                    Text('Length: ${MapsService.instance.apiKey.length} characters'),
                    const SizedBox(height: 8),
                    Text('Is Valid (Basic Check): ${MapsService.instance.isApiKeyValid}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'API Test Result',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    if (_isLoading)
                      const Row(
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 12),
                          Text('Testing API key...'),
                        ],
                      )
                    else
                      Text(
                        _testResult,
                        style: TextStyle(
                          color: _resultColor,
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            if (!_isLoading && _resultColor == Colors.red) ...[
              Card(
                color: Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'How to Fix:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.red.shade700,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '1. Go to Google Cloud Console\n'
                        '2. Enable these APIs:\n'
                        '   • Maps SDK for Android\n'
                        '   • Maps SDK for iOS\n'
                        '   • Geocoding API\n'
                        '   • Places API\n'
                        '3. Create/update API key\n'
                        '4. Update .env.development file',
                        style: TextStyle(color: Colors.red.shade600),
                      ),
                    ],
                  ),
                ),
              ),
            ],
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isLoading = true;
                  });
                  _testApiKey();
                },
                child: const Text('Test Again'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
